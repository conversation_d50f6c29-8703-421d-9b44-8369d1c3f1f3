Microsoft (R) Debugging Information Dumper  Version 14.00.23611
Copyright (C) Microsoft Corporation.  All rights reserved.


***** SECTION #1

*** SYMBOLS

(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj


*** FILECHKSUMS

FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** STRINGTABLE

00000000 
00000001 C:\Projects\FlashCpp\test_debug.cpp


***** SECTION #2

*** TYPES

0x1000 : Length = 14, Leaf = 0x1008 LF_PROCEDURE
	Return type = T_INT4(0074), Call type = C Near
	Func attr = none
	# Parms = 2, Arg list type = 0x1001

