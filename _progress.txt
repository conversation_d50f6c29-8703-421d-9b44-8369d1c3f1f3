C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - FILE ID AND STRING TABLE:
===============================================
cvdump output shows:
(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj
(000038) S_GPROC32_ID: [0003:00000000], Cb: 0000001A, ID: 0x1000, add
(000063) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000081) S_LOCAL: Param: 00000074, a
(00008D) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0008
(00009D) S_LOCAL: Param: 00000074, b
(0000A9) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0010
(0000B9) S_PROC_ID_END
(0000BD) S_GPROC32_ID: [0003:00000020], Cb: 00000026, ID: 0x1000, main
(0000E9) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000107) S_PROC_ID_END

*** FILECHKSUMS
FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** LINES
  0003:00000000-0000001A, flags = 0000, fileid = 00000000
      3 00000013
  fileid = 000000F2
CVDUMP : fatal error : Incorrect line block header

ANALYSIS:
========
✅ S_OBJNAME structure is CORRECT (signature and object name)
✅ S_GPROC32_ID structures are CORRECT (names, offsets, types all perfect)
✅ S_FRAMEPROC structures are CORRECT (frame size and flags)
✅ S_LOCAL and S_DEFRANGE_FRAMEPOINTER_REL are CORRECT
✅ FILECHKSUMS section is CORRECT (SHA-256 hash and file ID)
❌ CRITICAL: Line information has incorrect file ID (0xF2 = 242)
❌ CRITICAL: String table implementation needs fixing

ROOT CAUSE:
==========
1. Line information file ID mismatch:
   - First function uses correct file ID (0x00)
   - Second function uses incorrect file ID (0xF2)
   - This suggests the file ID mapping is not being properly maintained

2. String table issues:
   - String table is being written but may have incorrect offsets
   - Need to verify string table alignment and structure

PROGRESS UPDATE:
===============
✅ FIXED: Symbol records - all parsing perfectly
✅ FIXED: Function length calculation - functions added with correct lengths
✅ FIXED: Line information structure - separate subsections per function
✅ FIXED: LineInfoHeader size (12 bytes)
✅ FIXED: First function line information parsing

NEXT STEPS:
===========
1. Fix file ID mapping:
   - Verify file ID assignment in addSourceFile()
   - Ensure file IDs are consistent across all debug sections
   - Add validation to prevent invalid file IDs

2. Fix string table:
   - Verify string table alignment (4-byte boundary)
   - Ensure string offsets are calculated correctly
   - Add validation for string table entries

3. Add debug logging:
   - Log file ID assignments
   - Log string table operations
   - Log line information generation

4. Test cases:
   - Test with multiple source files
   - Test with functions in different files
   - Test with complex string table entries

BUILD AND TEST:
==============
1. Build the project:
   ```powershell
   .\build_flashcpp.bat
   ```

2. Run test case:
   ```powershell
   .\FlashCpp.exe test_debug.cpp
   ```

3. Verify debug info:
   ```powershell
   cvdump.exe test_debug.obj
   ```

4. Check for:
   - Correct file IDs in line information
   - Valid string table entries
   - No alignment issues
   - No buffer overruns


