C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - FILE ID AND STRING TABLE:
===============================================
cvdump output shows:
(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj
(000038) S_GPROC32_ID: [0003:00000000], Cb: 0000001A, ID: 0x1000, add
(000063) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000081) S_LOCAL: Param: 00000074, a
(00008D) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0008
(00009D) S_LOCAL: Param: 00000074, b
(0000A9) S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0010
(0000B9) S_PROC_ID_END
(0000BD) S_GPROC32_ID: [0003:00000020], Cb: 00000026, ID: 0x1000, main
(0000E9) S_FRAMEPROC: Frame size = 0x00000000 bytes
(000107) S_PROC_ID_END

*** FILECHKSUMS
FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** LINES
  0003:00000000-0000001A, flags = 0000, fileid = 00000000
      3 00000013
  fileid = 000000F2
CVDUMP : fatal error : Incorrect line block header

ANALYSIS:
========
✅ S_OBJNAME structure is CORRECT (signature and object name)
✅ S_GPROC32_ID structures are CORRECT (names, offsets, types all perfect)
✅ S_FRAMEPROC structures are CORRECT (frame size and flags)
✅ S_LOCAL and S_DEFRANGE_FRAMEPOINTER_REL are CORRECT
✅ FILECHKSUMS section is CORRECT (SHA-256 hash and file ID)
❌ CRITICAL: Line information has incorrect file ID (0xF2 = 242)
❌ CRITICAL: String table implementation needs fixing

PROGRESS UPDATE:
===============
✅ FIXED: Function ID assignment - unique IDs per function (0x1000, 0x1001)
✅ FIXED: String table generation - working correctly with proper offsets
✅ FIXED: Symbol records - all parsing perfectly
✅ FIXED: Function length calculation - functions added with correct lengths
✅ FIXED: Line information structure - separate subsections per function
✅ FIXED: LineInfoHeader size (12 bytes)
✅ FIXED: First function line information parsing

CURRENT STATUS:
==============
- Function IDs: ✅ Working (add=0x1000, main=0x1001)
- String table: ✅ Working (37 bytes, contains source file path)
- Debug sections: ✅ Generated (364 bytes .debug$S, 20 bytes .debug$T)
- File writing: ❌ COFFI save failing, manual fallback lacks debug sections

ROOT CAUSE:
==========
File writing issue:
- COFFI save is failing for unknown reason
- Manual fallback only creates basic COFF without debug sections
- Need to either fix COFFI save or enhance manual fallback

NEXT STEPS:
===========
1. Fix COFFI save issue:
   - Investigate why COFFI save is failing
   - Check if debug sections are causing the issue
   - Verify section flags and alignment

2. Alternative: Enhance manual fallback:
   - Add debug sections to manual COFF implementation
   - Include .debug$S and .debug$T sections
   - Maintain proper section ordering and alignment

3. Test with cvdump:
   - Once file writing works, verify with cvdump.exe
   - Check function IDs match reference (0x1000, 0x1001)
   - Verify string table contents

BUILD AND TEST:
==============
1. Build the project:
   ```powershell
   .\build_flashcpp.bat
   ```

2. Run test case:
   ```powershell
   .\FlashCpp.exe test_debug.cpp
   ```

3. Verify debug info:
   ```powershell
   cvdump.exe test_debug.obj
   ```

4. Check for:
   - Correct file IDs in line information
   - Valid string table entries
   - No alignment issues
   - No buffer overruns


