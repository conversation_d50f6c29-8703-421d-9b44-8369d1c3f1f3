int add(int a, int b)
{
return a + b;
}

int main()
{
return add(3, 5);
}
         
Creating simplified ObjectFileWriter for debugging...
Simplified ObjectFileWriter created successfully
DEBUG: Adding line mapping for line 1 (opcode: 57)
DEBUG: Line mapping SKIPPED (no current function) - line: 1
Adding function symbol: add at offset 0
DEBUG: Adding function to debug builder: add at offset 0
DEBUG: Function added to debug builder
Function symbol added successfully
DEBUG: Setting current function: add with file_id=0
DEBUG: Adding line mapping for line 3 (opcode: 56)
DEBUG: Line mapping added - function: add, offset: 19, line: 3
DEBUG: Adding line mapping for line 6 (opcode: 57)
DEBUG: Line mapping SKIPPED (no current function) - line: 6
Adding function symbol: main at offset 32
DEBUG: Adding function to debug builder: main at offset 32
DEBUG: Function added to debug builder
Function symbol added successfully
DEBUG: Finalizing previous function: add with file_id=0 and 1 lines
DEBUG: Updated function add with file_id=0
DEBUG: Setting current function: main with file_id=0
DEBUG: Adding line mapping for line 8 (opcode: 56)
DEBUG: Line mapping added - function: main, offset: 33, line: 8
Adding source file to debug info: 'C:\Projects\FlashCpp\test_debug.cpp'
Adding 70 bytes to section 0 (current size: 0)
Machine code bytes (70 total): 55 48 8b ec 48 83 ec 10 48 89 4d 10 48 89 55 18 48 01 d1 89 c8 48 89 ec 5d c3 90 90 90 90 90 90 55 48 8b ec 48 83 ec 30 48 b9 03 00 00 00 00 00 00 00 48 ba 05 00 00 00 00 00 00 00 e8 00 00 00 00 48 89 ec 5d c3 
finalize_debug_info: Generating debug information...
DEBUG: finalizeCurrentFunction: main with file_id=0 and 1 lines
DEBUG: finalizeCurrentFunction updated function main with file_id=0
DEBUG: Set text section number to 3
DEBUG: generateDebugS() called
DEBUG: Writing S_OBJNAME symbol
DEBUG: Using object name: C:\Projects\FlashCpp\test_debug.obj
DEBUG: S_OBJNAME symbol written, symbols_data size: 44
DEBUG: Number of functions: 2
DEBUG: Processing function: add
DEBUG: Function ID written: 0x1000 (4 bytes)
DEBUG: Writing function name: 'add' (length: 3)
DEBUG: Function proc_data size: 39 bytes
DEBUG: Function offset: 0, length: 26
DEBUG: Processing function: main
DEBUG: Function ID written: 0x1001 (4 bytes)
DEBUG: Writing function name: 'main' (length: 4)
DEBUG: Function proc_data size: 40 bytes
DEBUG: Function offset: 32, length: 38
DEBUG: Final symbols_data size before writeSubsection: 255
DEBUG: Writing subsection kind=241, unpadded_size=255, header_length=255, header_size=8
DEBUG: Header bytes: f1 00 00 00 ff 00 00 00 
DEBUG: Writing subsection kind=244, unpadded_size=40, header_length=40, header_size=8
DEBUG: Header bytes: f4 00 00 00 28 00 00 00 
DEBUG: Writing subsection kind=242, unpadded_size=32, header_length=32, header_size=8
DEBUG: Header bytes: f2 00 00 00 20 00 00 00 
DEBUG: Writing subsection kind=242, unpadded_size=32, header_length=32, header_size=8
DEBUG: Header bytes: f2 00 00 00 20 00 00 00 
DEBUG: Writing subsection kind=243, unpadded_size=37, header_length=37, header_size=8
DEBUG: Header bytes: f3 00 00 00 25 00 00 00 
Adding 444 bytes to section 6 (current size: 0)
Added 444 bytes of .debug$S data
Adding 20 bytes to section 7 (current size: 0)
Added 20 bytes of .debug$T data
Starting coffi_.save...
Number of sections: 3
Number of symbols: 4
Section 0: '.debug$S' size=444 flags=0x42100040
Section 1: '.debug$T' size=20 flags=0x42100040
Section 2: '.text$mn' size=70 flags=0x60500020
Symbol 0: .text$mn section=3 value=0x0
Symbol 1: @feat.00 section=65535 value=0x80010190
Symbol 2: add section=3 value=0x0
Symbol 3: main section=3 value=0x20
COFFI save failed! Attempting manual fallback...
Using manual COFF implementation...
Failed to open file for writing: 
Error writing object file: Failed to save object file with both COFFI and manual fallback
