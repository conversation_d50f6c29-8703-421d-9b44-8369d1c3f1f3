Microsoft (R) Debugging Information Dumper  Version 14.00.23611
Copyright (C) Microsoft Corporation.  All rights reserved.


***** SECTION #1

*** SYMBOLS

(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj

(000038) S_GPROC32_ID: [0003:00000000], Cb: 0000001A, ID:             0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014

(000063)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=default Param=default (0x00114200)

(000081) S_PROC_ID_END

(000085) S_GPROC32_ID: [0003:00000020], Cb: 00000026, ID:             0x1000, main
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014

(0000B1)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=default Param=default (0x00114200)

(0000CF) S_PROC_ID_END


*** FILECHKSUMS

FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** LINES

  0003:00000000-0000001A, flags = 0000, fileid = 00000000

      3 00000013

  fileid = 00000020

      1 00000000     33 00000014    243 80000008

 <<<< WARNING >>>> 1 line/addr pairs are out of bounds!

  fileid = 00000025
CVDUMP : fatal error : Incorrect line block header

