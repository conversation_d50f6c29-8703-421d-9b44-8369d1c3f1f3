Microsoft (R) Debugging Information Dumper  Version 14.00.23611
Copyright (C) Microsoft Corporation.  All rights reserved.


***** SECTION #1

*** SYMBOLS

(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj

(000038) S_GPROC32_ID: [0001:00000000], Cb: 0000001A, ID:             0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014

(000064)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=default Param=default (0x00114200)
(000082)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=default Param=default (0x00114200)

(000084) S_PROC_ID_END

(000088) S_GPROC32_ID: [0001:00000020], Cb: 00000026, ID:             0x1000, main
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014

(0000B5)  S_GPROC32_ID: [0001:00000020], Cb: 00000026, ID:             0x1000, main
          Parent: 00000000, End: 00000000, Next: 00000000
          Debug start: 00000008, Debug end: 00000014
cbSymSeg: 37	cbRec: 7168	RecType: 0x1147
CVDUMP : fatal error : Overran end of symbol table

